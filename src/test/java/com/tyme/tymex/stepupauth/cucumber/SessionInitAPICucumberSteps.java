package com.tyme.tymex.stepupauth.cucumber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.DeviceBioEnrollStatus;
import com.tyme.tymex.stepupauth.domain.OtpAuthConfig;
import com.tyme.tymex.stepupauth.domain.PasscodeAuthConfig;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.connector.model.DeviceIdType;
import com.tyme.tymex.stepupauth.infra.connector.model.profile.ProfilePhoneData;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.repository.LinkDeviceInfoRepo;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Log4j2
@SpringBootTest
public class SessionInitAPICucumberSteps extends StepUpAuthApplicationTestsBase {

    @Autowired
    private StepUpService stepUpService;

    @Autowired
    private StepUpRepo stepUpRepo;

    @Autowired
    private LinkDeviceInfoRepo linkDeviceInfoRepo;

    @Autowired
    private StepUpAuthDeviceBioEnrollmentRepo deviceBioEnrollmentRepo;

    @Autowired
    private ObjectMapper objectMapper;

    // DynamoDB tables for direct data insertion in tests
    @Autowired
    private software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable<LinkDeviceInfo> linkDeviceInfoTable;

    private String profileId;
    private String flowId;
    private String flowName;
    private String appId;
    private InitStepUpSessionDto initRequest;
    private StepUpSessionResponse sessionResponse;
    private Exception thrownException;
    private ProfileInfo profileInfo;

    @Before
    public void init() {
        profileId = "test-profile-" + UUID.randomUUID();
        flowId = "test-flow-" + UUID.randomUUID();
        flowName = "TEST_FLOW";
        appId = "test-app";
        initRequest = null;
        sessionResponse = null;
        thrownException = null;
        profileInfo = null;
    }

    @After
    public void cleanup() {
        cleanupTestData();
    }

    // ========================================
    // GIVEN STEP DEFINITIONS
    // ========================================

    @Given("A valid profile exists in the system")
    public void aValidProfileExistsInTheSystem() {
        // Profile will be mocked by the MockWebServer in InitialExtension
        // The mock server will return valid profile data for profileIds starting with "test-profile-"
        profileInfo = ProfileInfo.builder()
                .id(profileId)
                .personPhoneData(ProfilePhoneData.builder()
                        .dialCode("+84")
                        .phoneNumber("+84909418781")
                        .build())
                .build();
        log.info("Profile service mock server will return valid profile info for profileId: {}", profileId);
    }

    @Given("The profile has valid {string} information")
    public void theProfileHasValidInformation(String dataType) {
        switch (dataType) {
            case "phone":
                Assertions.assertNotNull(profileInfo);
                Assertions.assertNotNull(profileInfo.personPhoneData());
                Assertions.assertNotNull(profileInfo.personPhoneData().dialCode());
                Assertions.assertNotNull(profileInfo.personPhoneData().phoneNumber());
                log.info("Profile has valid phone data: dialCode={}, phoneNumber={}",
                        profileInfo.personPhoneData().dialCode(),
                        profileInfo.personPhoneData().phoneNumber());
                break;
            case "device":
                setupDeviceBioTestData();
                log.info("Profile has valid device information for DEVICE_BIO factor - test data created");
                break;
            case "facial":
                log.info("Profile has valid facial information for FACIAL factor");
                break;
            default:
                log.warn("Unknown data type: {}", dataType);
        }
    }

    @Given("A profile does not exist in the system")
    public void aProfileDoesNotExistInTheSystem() {
        profileId = "non-existent-profile-" + UUID.randomUUID();
        log.info("Using non-existent profileId: {} - mock server will return 404", profileId);
    }

    @Given("A profile exists but has no {string} information")
    public void aProfileExistsButHasNoInformation(String dataType) {
        switch (dataType) {
            case "phone number":
                profileId = "test-profile-no-phone-" + UUID.randomUUID();
                log.info("Using profileId without phone data: {}", profileId);
                break;
            case "device":
                profileId = "test-profile-no-device-" + UUID.randomUUID();
                log.info("Using profileId without device data: {}", profileId);
                break;
            default:
                log.warn("Unknown data type: {}", dataType);
        }
    }

    // ========================================
    // WHEN STEP DEFINITIONS
    // ========================================

    @When("I initialize a step-up session with {string} factor and {string} with authConfig {string}")
    public void iInitializeAStepUpSessionWithFactorAndIdentifierWithAuthConfig(String factor, String identifierType, String authConfigParams) {

        AuthFactor authFactor = AuthFactor.valueOf(factor);
        IdentifierType idType = IdentifierType.valueOf(identifierType);

        Map<AuthFactor, Object> authConfig = new HashMap<>();
        setupAuthConfigForFactorWithParams(authFactor, authConfig, authConfigParams);

        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(authFactor)
                        .build()))
                .authConfig(authConfig)
                .identifierId(profileId)
                .identifierType(idType)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with {} factor and {} for profileId: {} with authConfig: {}",
                factor, identifierType, profileId, authConfigParams);

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during {} session initialization: {}", factor, e.getMessage());
        }
    }

    @When("I initialize a step-up session with {string} factor and {string}")
    public void iInitializeAStepUpSessionWithFactorAndIdentifier(String factor, String identifierType) {

        AuthFactor authFactor = AuthFactor.valueOf(factor);
        IdentifierType idType = IdentifierType.valueOf(identifierType);

        Map<AuthFactor, Object> authConfig = new HashMap<>();
        setupAuthConfigForFactor(authFactor, authConfig);

        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(authFactor)
                        .build()))
                .authConfig(authConfig)
                .identifierId(profileId)
                .identifierType(idType)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with {} factor and {} for profileId: {}", factor, identifierType, profileId);

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during {} session initialization: {}", factor, e.getMessage());
        }
    }

    // ========================================
    // THEN STEP DEFINITIONS
    // ========================================

    @Then("The session should be created successfully")
    public void theSessionShouldBeCreatedSuccessfully() {
        Assertions.assertNull(thrownException, "No exception should be thrown");
        Assertions.assertNotNull(sessionResponse, "Session response should not be null");
        Assertions.assertNotNull(sessionResponse.getStepUpAuthId(), "Step-up auth ID should not be null");
        log.info("Session created successfully with ID: {}", sessionResponse.getStepUpAuthId());
    }

    @Then("The session should have {string} factor configured")
    public void theSessionShouldHaveFactorConfigured(String factor) throws JsonProcessingException {
        AuthFactor authFactor = AuthFactor.valueOf(factor);

        Assertions.assertNotNull(sessionResponse.getAuthFactorRules(), "Auth factor rules should not be null");
        Assertions.assertFalse(sessionResponse.getAuthFactorRules().isEmpty(), "Auth factor rules should not be empty");

        List<AuthFactorRule> authFactorRules = objectMapper.readValue(
                sessionResponse.getAuthFactorRules(),
                new TypeReference<List<AuthFactorRule>>() {}
        );

        boolean hasExpectedFactor = authFactorRules.stream()
                .anyMatch(rule -> rule.getFactor() == authFactor);
        Assertions.assertTrue(hasExpectedFactor, "Session should have " + factor + " factor configured");
        log.info("Session has {} factor configured", factor);
    }

    @Then("The {string} configuration should be properly set with {string}")
    public void theConfigurationShouldBeProperlySetWith(String factor, String configValidation) {
        AuthFactor authFactor = AuthFactor.valueOf(factor);

        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");

        Object factorConfig = sessionEntity.getFactorConfigs().get(authFactor);
        Assertions.assertNotNull(factorConfig, factor + " config should not be null");

        verifyFactorConfigValidation(authFactor, configValidation, factorConfig);

        log.info("{} configuration has been properly set with {}", factor, configValidation);
    }

    @Then("The session status should be IN_PROGRESS")
    public void theSessionStatusShouldBeINPROGRESS() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(StepUpStatus.IN_PROGRESS, sessionEntity.getOverallStatus(),
                "Session status should be IN_PROGRESS");
        log.info("Session status is IN_PROGRESS");
    }

    @Then("The current factor should be {string}")
    public void theCurrentFactorShouldBe(String factor) {
        AuthFactor authFactor = AuthFactor.valueOf(factor);

        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(authFactor, sessionEntity.getCurrentFactor(),
                "Current factor should be " + factor);
        log.info("Current factor is {}", factor);
    }

    @Then("The service should return an error indicating profile not found")
    public void theServiceShouldReturnAnErrorIndicatingProfileNotFound() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        Assertions.assertTrue(thrownException instanceof DomainException,
                "Exception should be DomainException");

        DomainException domainException = (DomainException) thrownException;
        Assertions.assertEquals(ErrorCode.PROFILE_NOT_FOUND, domainException.getErrorCode(),
                "Error code should be PROFILE_NOT_FOUND");
        log.info("Service correctly returned PROFILE_NOT_FOUND error");
    }

    @Then("The service should return an error indicating invalid profile data")
    public void theServiceShouldReturnAnErrorIndicatingInvalidProfileData() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        // The specific error handling for missing phone data may vary based on implementation
        log.info("Service correctly returned error for invalid profile data: {}", thrownException.getMessage());
    }

    // ========================================
    // HELPER METHODS
    // ========================================

    /**
     * Helper method to setup authConfig based on factor type with default values
     */
    private void setupAuthConfigForFactor(AuthFactor factor, Map<AuthFactor, Object> authConfig) {
        switch (factor) {
            case OTP:
                break;
            case DEVICE_BIO:
                DeviceBioAuthConfig deviceBioConfig = DeviceBioAuthConfig.builder()
                        .deviceId("ad4c48c67836ff97")
                        .internalDeviceId("ad4c48c67836ff97")
                        .linkDeviceDays(10L)
                        .deviceBioEnrollDays(10L)
                        .build();
                authConfig.put(AuthFactor.DEVICE_BIO, deviceBioConfig);
                break;
            case PASSCODE:
                PasscodeAuthConfig passcodeConfig = PasscodeAuthConfig.builder()
                        .username("test-user")
                        .build();
                authConfig.put(AuthFactor.PASSCODE, passcodeConfig);
                break;
            case FACIAL:
                Map<String, Object> facialConfig = new HashMap<>();
                facialConfig.put("numberOfLivenessCheck", 5);
                facialConfig.put("numberOfComparison", 3);
                facialConfig.put("enrollment", "INACTIVE");
                facialConfig.put("segmentTracking", "INACTIVE");
                facialConfig.put("triggerPoint", "INTRO");
                facialConfig.put("type", "SELFIE_LIVENESS_CHECK_COMPARISON");
                authConfig.put(AuthFactor.FACIAL, facialConfig);
                break;
            default:
                log.warn("No authConfig setup defined for factor: {}", factor);
        }
    }

    /**
     * Helper method to setup authConfig based on factor type and parameters from feature file
     */
    private void setupAuthConfigForFactorWithParams(AuthFactor factor, Map<AuthFactor, Object> authConfig, String authConfigParams) {
        Map<String, String> params = parseAuthConfigParams(authConfigParams);

        switch (factor) {
            case OTP:
                OtpAuthConfig.OtpAuthConfigBuilder otpBuilder = OtpAuthConfig.builder();
                if (params.containsKey("dialCode")) otpBuilder.dialCode(params.get("dialCode"));
                if (params.containsKey("cellphone")) otpBuilder.cellphone(params.get("cellphone"));
                // Note: channel and purpose are not fields in OtpAuthConfig, they are ignored
                authConfig.put(AuthFactor.OTP, otpBuilder.build());
                break;

            case DEVICE_BIO:
                DeviceBioAuthConfig.DeviceBioAuthConfigBuilder deviceBioBuilder = DeviceBioAuthConfig.builder();
                if (params.containsKey("deviceId")) deviceBioBuilder.deviceId(params.get("deviceId"));
                if (params.containsKey("internalDeviceId")) deviceBioBuilder.internalDeviceId(params.get("internalDeviceId"));
                if (params.containsKey("linkDeviceDays")) deviceBioBuilder.linkDeviceDays(Long.parseLong(params.get("linkDeviceDays")));
                if (params.containsKey("deviceBioEnrollDays")) deviceBioBuilder.deviceBioEnrollDays(Long.parseLong(params.get("deviceBioEnrollDays")));
                authConfig.put(AuthFactor.DEVICE_BIO, deviceBioBuilder.build());
                break;

            case PASSCODE:
                PasscodeAuthConfig.PasscodeAuthConfigBuilder passcodeBuilder = PasscodeAuthConfig.builder();
                if (params.containsKey("username")) {
                    passcodeBuilder.username(params.get("username"));
                } else if (params.containsKey("deviceId")) {
                    // Map deviceId to username for compatibility with feature file
                    passcodeBuilder.username("user-" + params.get("deviceId"));
                }
                authConfig.put(AuthFactor.PASSCODE, passcodeBuilder.build());
                break;

            case FACIAL:
                // FACIAL factor doesn't have a specific config class yet
                // Use a Map for now to store facial configuration
                Map<String, Object> facialConfigMap = new HashMap<>();
                if (params.containsKey("numberOfLivenessCheck")) facialConfigMap.put("numberOfLivenessCheck", Integer.parseInt(params.get("numberOfLivenessCheck")));
                if (params.containsKey("numberOfComparison")) facialConfigMap.put("numberOfComparison", Integer.parseInt(params.get("numberOfComparison")));
                if (params.containsKey("enrollment")) facialConfigMap.put("enrollment", params.get("enrollment"));
                if (params.containsKey("segmentTracking")) facialConfigMap.put("segmentTracking", params.get("segmentTracking"));
                if (params.containsKey("triggerPoint")) facialConfigMap.put("triggerPoint", params.get("triggerPoint"));
                if (params.containsKey("type")) facialConfigMap.put("type", params.get("type"));
                authConfig.put(AuthFactor.FACIAL, facialConfigMap);
                break;

            default:
                log.warn("No authConfig setup defined for factor: {}", factor);
        }
    }

    /**
     * Helper method to parse authConfig parameters from comma-separated string
     */
    private Map<String, String> parseAuthConfigParams(String authConfigParams) {
        Map<String, String> params = new HashMap<>();
        if (authConfigParams != null && !authConfigParams.trim().isEmpty()) {
            String[] pairs = authConfigParams.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0].trim(), keyValue[1].trim());
                }
            }
        }
        return params;
    }

    /**
     * Helper method to verify factor config validation based on factor type and validation type
     */
    private void verifyFactorConfigValidation(AuthFactor factor, String configValidation, Object factorConfig) {
        switch (factor) {
            case OTP:
                if ("enriched with profile data".equals(configValidation)) {
                    // Verify OTP config has been enriched with profile phone data
                    OtpAuthConfig otpConfig = objectMapper.convertValue(factorConfig, OtpAuthConfig.class);
                    Assertions.assertNotNull(otpConfig.getCellphone(), "OTP config should have cellphone");
                    Assertions.assertNotNull(otpConfig.getDialCode(), "OTP config should have dialCode");
                    log.info("OTP config has been enriched with profile phone data");
                }
                break;

            case DEVICE_BIO:
                if ("provided device data".equals(configValidation)) {
                    DeviceBioAuthConfig deviceBioConfig = objectMapper.convertValue(factorConfig, DeviceBioAuthConfig.class);
                    Assertions.assertEquals("ad4c48c67836ff97", deviceBioConfig.getDeviceId(), "Device ID should match");
                    Assertions.assertEquals("ad4c48c67836ff97", deviceBioConfig.getInternalDeviceId(), "Internal Device ID should match");
                    Assertions.assertEquals(1L, deviceBioConfig.getLinkDeviceDays(), "Link device days should match");
                    Assertions.assertEquals(1L, deviceBioConfig.getDeviceBioEnrollDays(), "Device bio enroll days should match");
                    log.info("DEVICE_BIO config contains the provided device data");
                }
                break;

            case PASSCODE:
                if ("device information".equals(configValidation)) {
                    PasscodeAuthConfig passcodeConfig = objectMapper.convertValue(factorConfig, PasscodeAuthConfig.class);
                    Assertions.assertNotNull(passcodeConfig.getUsername(), "Username should not be null");
                    Assertions.assertTrue(passcodeConfig.getUsername().contains("ad4c48c67836ff97") ||
                                        passcodeConfig.getUsername().equals("test-user"),
                                        "Username should contain device reference or be test-user");
                    log.info("PASSCODE config contains device information mapped to username: {}", passcodeConfig.getUsername());
                }
                break;

            case FACIAL:
                if ("facial recognition config".equals(configValidation)) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> facialConfigMap = (Map<String, Object>) factorConfig;
                    Assertions.assertEquals(5, facialConfigMap.get("numberOfLivenessCheck"), "Number of liveness check should match");
                    Assertions.assertEquals(3, facialConfigMap.get("numberOfComparison"), "Number of comparison should match");
                    Assertions.assertEquals("INACTIVE", facialConfigMap.get("enrollment"), "Enrollment should match");
                    Assertions.assertEquals("INACTIVE", facialConfigMap.get("segmentTracking"), "Segment tracking should match");
                    Assertions.assertEquals("INTRO", facialConfigMap.get("triggerPoint"), "Trigger point should match");
                    Assertions.assertEquals("SELFIE_LIVENESS_CHECK_COMPARISON", facialConfigMap.get("type"), "Type should match");
                    log.info("FACIAL config contains facial recognition configuration");
                }
                break;

            default:
                log.warn("No validation defined for factor: {} with configValidation: {}", factor, configValidation);
        }
    }

    /**
     * Helper method to setup test data for DEVICE_BIO factor eligibility
     * Creates LinkDeviceInfo and StepUpAuthDeviceBioEnrollment records in DynamoDB
     */
    private void setupDeviceBioTestData() {
        try {
            LinkDeviceInfo linkDeviceInfo = LinkDeviceInfo.builder()
                    .partitionKey(GlobalUtils.toLinkDeviceInfoPk(profileId))
                    .sk(GlobalUtils.toLinkDeviceInfoSk(appId))
                    .deviceId("ad4c48c67836ff97")
                    .deviceIdType(DeviceIdType.AUTH_DEVICE_ID)
                    .deviceOs("iOS")
                    .linkedAt(Instant.now().minus(20, ChronoUnit.DAYS).toEpochMilli()) // Linked 5 days ago
                    .build();

            linkDeviceInfoTable.putItem(linkDeviceInfo);
            log.info("Saved LinkDeviceInfo test data for profileId: {}, deviceId: {}", profileId, "ad4c48c67836ff97");

            StepUpAuthDeviceBioEnrollment deviceBioEnrollment = StepUpAuthDeviceBioEnrollment.builder()
                    .pk(GlobalUtils.toDeviceBioEnrollmentPk(profileId))
                    .sk(GlobalUtils.getDeviceBioEnrollmentSk())
                    .deviceId("ad4c48c67836ff97")
                    .channel("iOS")
                    .biometricType("Facial")
                    .flowName(flowName)
                    .status(DeviceBioEnrollStatus.ENROLLED.name())
                    .securityLevel("1")
                    .eventTime(Instant.now().minus(3, ChronoUnit.DAYS).toEpochMilli()) // Enrolled 3 days ago
                    .build();

            deviceBioEnrollmentRepo.saveDeviceBioEnrollment(deviceBioEnrollment);
            log.info("Created StepUpAuthDeviceBioEnrollment test data for profileId: {}, status: {}",
                    profileId, DeviceBioEnrollStatus.ENROLLED.name());

        } catch (Exception e) {
            log.error("Failed to setup DEVICE_BIO test data: {}", e.getMessage(), e);
        }
    }

    /**
     * Helper method to clean up test data after each scenario
     */
    private void cleanupTestData() {
        try {

            if (profileId != null && appId != null) {
                software.amazon.awssdk.enhanced.dynamodb.Key linkDeviceKey =
                    software.amazon.awssdk.enhanced.dynamodb.Key.builder()
                        .partitionValue(GlobalUtils.toLinkDeviceInfoPk(profileId))
                        .sortValue(GlobalUtils.toLinkDeviceInfoSk(appId))
                        .build();
                linkDeviceInfoTable.deleteItem(linkDeviceKey);
                log.info("Cleaned up LinkDeviceInfo for profileId: {}, appId: {}", profileId, appId);
            }

            if (profileId != null) {
                log.info("DeviceBioEnrollment cleanup skipped - will be overwritten if needed");
            }

        } catch (Exception e) {
            log.warn("Failed to cleanup test data: {}", e.getMessage());

        }
    }
}