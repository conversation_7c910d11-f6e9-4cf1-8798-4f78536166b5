Feature: Initialization of a Step-Up Session
  As a client application
  I want to initialize a step-up authentication session
  So that I can authenticate users with various factors

  Scenario Outline: Successfully initialize session with factor and identifier
    Given A valid profile exists in the system
    And The profile has valid "<dataType>" information
    When I initialize a step-up session with "<factor>" factor and "<identifierType>" with authConfig "<authConfigParams>"
    Then The session should be created successfully
    And The session should have "<factor>" factor configured
    And The "<factor>" configuration should be properly set with "<configValidation>"
    And The session status should be "<sessionStatus>"
    And The current factor should be "<factor>"

    Examples:
      | factor     | identifierType |sessionStatus|dataType | authConfigParams                                                                                                                                    | configValidation           |
      | OTP        | PROFILE_ID     |IN_PROGRESS   |phone    | channel:SMS,dialCode:+63,cellphone:+632134422221,purpose:verify your cellphone number                                                              | enriched with profile data |
      | DEVICE_BIO | PROFILE_ID     |IN_PROGRESS   |device   | deviceId:ad4c48c67836ff97,internalDeviceId:ad4c48c67836ff97,linkDeviceDays:1,deviceBioEnrollDays:1                                            | provided device data       |
      | PASSCODE   | PROFILE_ID     |IN_PROGRESS   |device   | deviceId:ad4c48c67836ff97                                                                                                                           | device information         |
      | FACIAL     | PROFILE_ID     |IN_PROGRESS  |facial   | numberOfLivenessCheck:5,numberOfComparison:3,enrollment:INACTIVE,segmentTracking:INACTIVE,triggerPoint:INTRO,type:SELFIE_LIVENESS_CHECK_COMPARISON | facial recognition config  |